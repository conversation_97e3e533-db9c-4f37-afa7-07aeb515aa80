# 量化特征工程文档

## 特征分类与标准化说明

### 1. 基础价格特征
| 特征名 | 描述 | 原始范围 | 标准化方法 | 标准化后范围 |
|--------|------|----------|------------|--------------|
| returns | 日收益率 | -∞ ~ +∞ | 无 | -∞ ~ +∞ |
| log_returns | 对数收益率 | -∞ ~ +∞ | 无 | -∞ ~ +∞ |

### 2. 技术指标特征

#### 2.1 趋势类指标
| 特征名 | 描述 | 原始范围 | 标准化方法 | 标准化后范围 |
|--------|------|----------|------------|--------------|
| rsi | RSI相对强弱指数 | 0 ~ 100 | (rsi-50)/50 | -1 ~ 1 |
| ma_ratio_X_norm | X日移动平均比率 | 0.5 ~ 2.0 | log(ratio) | -0.7 ~ 0.7 |
| ema_ratio_X_norm | X日EMA比率 | 0.5 ~ 2.0 | log(ratio) | -0.7 ~ 0.7 |
| adx_norm | 平均趋向指数 | 0 ~ 100 | adx/100 | 0 ~ 1 |

#### 2.2 动量类指标
| 特征名 | 描述 | 原始范围 | 标准化方法 | 标准化后范围 |
|--------|------|----------|------------|--------------|
| macd_norm | MACD主线 | -∞ ~ +∞ | tanh(macd/std) | -1 ~ 1 |
| macd_signal_norm | MACD信号线 | -∞ ~ +∞ | tanh(signal/std) | -1 ~ 1 |
| macd_hist_norm | MACD柱状图 | -∞ ~ +∞ | tanh(hist/std) | -1 ~ 1 |
| willr_norm | 威廉指标 | -100 ~ 0 | (willr+50)/50 | -1 ~ 1 |
| momentum_norm | 动量指标 | -∞ ~ +∞ | tanh(mom/std) | -1 ~ 1 |
| momentum_Xd_norm | X日动量 | -∞ ~ +∞ | tanh(mom/std) | -1 ~ 1 |

#### 2.3 波动率类指标
| 特征名 | 描述 | 原始范围 | 标准化方法 | 标准化后范围 |
|--------|------|----------|------------|--------------|
| bb_position | 布林带位置 | 0 ~ 1 | 无需标准化 | 0 ~ 1 |
| atr_ratio_norm | ATR比率 | 0 ~ 0.1 | atr_ratio/mean | 0.5 ~ 2.0 |
| volatility_norm | 波动率 | 0 ~ 0.1 | vol/mean | 0.5 ~ 2.0 |
| high_low_ratio_norm | 日内波动比率 | 0 ~ 0.2 | ratio/mean | 0.5 ~ 2.0 |
| vol_regime_norm | 波动率状态 | 0.5 ~ 2.0 | log(ratio) | -0.7 ~ 0.7 |

#### 2.4 成交量类指标
| 特征名 | 描述 | 原始范围 | 标准化方法 | 标准化后范围 |
|--------|------|----------|------------|--------------|
| volume_ratio_norm | 成交量比率 | 0 ~ 10 | log(1+ratio) | 0 ~ 2.4 |
| amount_ratio | 成交额变化率 | -∞ ~ +∞ | 无 | -∞ ~ +∞ |

### 3. 市场结构特征

#### 3.1 排名类特征
| 特征名 | 描述 | 原始范围 | 标准化方法 | 标准化后范围 |
|--------|------|----------|------------|--------------|
| price_rank | 价格分位数排名 | 0 ~ 1 | 无需标准化 | 0 ~ 1 |
| volume_rank | 成交量分位数排名 | 0 ~ 1 | 无需标准化 | 0 ~ 1 |

#### 3.2 统计特征
| 特征名 | 描述 | 原始范围 | 标准化方法 | 标准化后范围 |
|--------|------|----------|------------|--------------|
| return_skew_norm | 收益率偏度 | -3 ~ 3 | tanh(skew/2) | -1 ~ 1 |
| return_kurt_norm | 收益率峰度 | 1 ~ 10 | tanh((kurt-3)/3) | -1 ~ 1 |

#### 3.3 市场行为特征
| 特征名 | 描述 | 原始范围 | 标准化方法 | 标准化后范围 |
|--------|------|----------|------------|--------------|
| mean_reversion_signal | 均值回归信号 | -1 ~ 1 | 无需标准化 | -1 ~ 1 |
| trend_strength | 趋势强度 | 0 ~ 1 | 无需标准化 | 0 ~ 1 |
| support_distance | 支撑位距离 | 0 ~ 1 | 无需标准化 | 0 ~ 1 |
| resistance_distance | 阻力位距离 | 0 ~ 1 | 无需标准化 | 0 ~ 1 |
| liquidity_score | 流动性评分 | 0 ~ 1 | 无需标准化 | 0 ~ 1 |

## 标准化方法说明

### 1. 线性标准化
- **公式**: (x - min) / (max - min)
- **适用**: 有明确上下界的指标
- **示例**: RSI, ADX

### 2. 对数标准化
- **公式**: log(x) 或 log(1 + x)
- **适用**: 比率类指标，通常大于0
- **示例**: 移动平均比率, 成交量比率

### 3. Tanh标准化
- **公式**: tanh(x / std)
- **适用**: 无界指标，需要压缩到[-1,1]
- **示例**: MACD, 动量指标

### 4. 分位数标准化
- **公式**: rank(x) / count(x)
- **适用**: 需要相对排名的指标
- **示例**: 价格排名, 成交量排名

### 5. Z-score标准化
- **公式**: (x - mean) / std
- **适用**: 正态分布的指标
- **示例**: 收益率相关指标

## 特征工程最佳实践

### 1. 避免前瞻偏差
- 所有指标计算只使用历史数据
- 标准化统计量只基于训练集计算
- 滚动窗口确保时间一致性

### 2. 处理缺失值
- 技术指标初期会有NaN值
- 使用dropna()清理数据
- 确保足够的有效样本数

### 3. 特征选择建议
- 优先使用标准化后的特征
- 避免高度相关的特征
- 考虑特征的经济意义

### 4. 模型输入建议
- 使用标准化后的特征训练模型
- 定期重新计算标准化参数
- 监控特征分布的稳定性

## 新增特征的价值

### 1. MACD系列
- 捕捉价格动量变化
- 识别趋势转折点
- 提供买卖信号

### 2. 多周期动量
- 不同时间尺度的价格变化
- 短期vs长期趋势对比
- 动量衰减检测

### 3. 市场结构特征
- 价格在历史分布中的位置
- 支撑阻力位的技术分析
- 流动性和市场深度

### 4. 统计特征
- 收益率分布特征
- 风险调整后的收益
- 波动率聚集效应

这些特征基于最新的量化研究成果，能够更好地捕捉市场的复杂动态。
