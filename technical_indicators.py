"""
技术指标计算模块
将各个技术指标拆分为独立函数，便于测试和维护
"""
import pandas as pd
import numpy as np
from typing import Union, Tuple, Optional


def calculate_rsi(close: pd.Series, window: int = 14) -> pd.Series:
    """
    计算相对强弱指数 (RSI)
    
    Args:
        close: 收盘价序列
        window: 计算窗口，默认14
        
    Returns:
        RSI值序列，范围 0-100
        
    标准化建议: (rsi - 50) / 50，将范围转换为 [-1, 1]
    """
    delta = close.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / (loss + 1e-8)  # 避免除零
    rsi = 100 - (100 / (1 + rs))
    return rsi


def calculate_bollinger_bands(close: pd.Series, window: int = 20, std_dev: float = 2.0) -> <PERSON><PERSON>[pd.Series, pd.Series, pd.Series]:
    """
    计算布林带
    
    Args:
        close: 收盘价序列
        window: 移动平均窗口，默认20
        std_dev: 标准差倍数，默认2.0
        
    Returns:
        (上轨, 中轨, 下轨)
        
    布林带位置计算: (close - lower) / (upper - lower)，范围 0-1，天然标准化
    """
    middle = close.rolling(window).mean()
    std = close.rolling(window).std()
    upper = middle + (std * std_dev)
    lower = middle - (std * std_dev)
    return upper, middle, lower


def calculate_bb_position(close: pd.Series, window: int = 20, std_dev: float = 2.0) -> pd.Series:
    """
    计算布林带位置
    
    Returns:
        布林带位置，范围 0-1，天然标准化
    """
    upper, middle, lower = calculate_bollinger_bands(close, window, std_dev)
    position = (close - lower) / (upper - lower + 1e-8)
    return position.clip(0, 1)  # 确保在 [0,1] 范围内


def calculate_moving_average_ratios(close: pd.Series, windows: list = [5, 10, 20, 30]) -> pd.DataFrame:
    """
    计算多周期移动平均比率
    
    Args:
        close: 收盘价序列
        windows: 移动平均窗口列表
        
    Returns:
        包含各周期MA比率的DataFrame
        
    标准化建议: np.log(ratio)，因为比率通常在 0.5-2.0 范围内
    """
    ratios = pd.DataFrame(index=close.index)
    for window in windows:
        ma = close.rolling(window).mean()
        ratios[f'ma_ratio_{window}'] = close / ma
    return ratios


def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
    """
    计算平均真实波幅 (ATR)
    
    Args:
        high: 最高价序列
        low: 最低价序列  
        close: 收盘价序列
        window: 计算窗口，默认14
        
    Returns:
        ATR值序列
        
    ATR比率: atr / close，范围通常 0-0.1
    标准化建议: atr_ratio / rolling_mean(atr_ratio, 252)
    """
    tr1 = high - low
    tr2 = abs(high - close.shift(1))
    tr3 = abs(low - close.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = tr.rolling(window).mean()
    return atr


def calculate_atr_ratio(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
    """
    计算ATR比率 (标准化的ATR)
    
    Returns:
        ATR比率，已进行标准化处理
    """
    atr = calculate_atr(high, low, close, window)
    atr_ratio = atr / close
    # 使用252日滚动均值进行标准化
    normalized_atr = atr_ratio / (atr_ratio.rolling(252).mean() + 1e-8)
    return normalized_atr


def calculate_volume_ratio(volume: pd.Series, window: int = 20) -> pd.Series:
    """
    计算成交量比率
    
    Args:
        volume: 成交量序列
        window: 移动平均窗口，默认20
        
    Returns:
        成交量比率
        
    范围: 通常 0-10
    标准化建议: np.log(1 + volume_ratio)
    """
    volume_ma = volume.rolling(window).mean()
    volume_ratio = volume / (volume_ma + 1e-8)
    return volume_ratio


def calculate_volatility(returns: pd.Series, window: int = 20) -> pd.Series:
    """
    计算滚动波动率
    
    Args:
        returns: 收益率序列
        window: 计算窗口，默认20
        
    Returns:
        波动率序列
        
    范围: 通常 0-0.1
    标准化建议: volatility / rolling_mean(volatility, 252)
    """
    volatility = returns.rolling(window).std()
    # 年化波动率
    annualized_vol = volatility * np.sqrt(252)
    return annualized_vol


def calculate_high_low_ratio(high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
    """
    计算日内波动比率
    
    Returns:
        日内波动比率
        
    范围: 通常 0-0.2
    标准化建议: ratio / rolling_mean(ratio, 252)
    """
    ratio = (high - low) / close
    return ratio


def calculate_macd(close: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    计算MACD指标
    
    Args:
        close: 收盘价序列
        fast: 快线周期，默认12
        slow: 慢线周期，默认26
        signal: 信号线周期，默认9
        
    Returns:
        (MACD线, 信号线, 柱状图)
        
    标准化建议: np.tanh(macd / rolling_std(macd, 252))
    """
    ema_fast = close.ewm(span=fast).mean()
    ema_slow = close.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram


def calculate_williams_r(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
    """
    计算威廉指标 (%R)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        window: 计算窗口，默认14
        
    Returns:
        Williams %R值，范围 -100 到 0
        
    标准化建议: (willr + 50) / 50，将范围转换为 [-1, 1]
    """
    highest_high = high.rolling(window).max()
    lowest_low = low.rolling(window).min()
    willr = -100 * (highest_high - close) / (highest_high - lowest_low + 1e-8)
    return willr


def calculate_momentum(close: pd.Series, window: int = 10) -> pd.Series:
    """
    计算动量指标
    
    Args:
        close: 收盘价序列
        window: 计算窗口，默认10
        
    Returns:
        动量值
        
    标准化建议: np.tanh(momentum / rolling_std(momentum, 252))
    """
    momentum = close - close.shift(window)
    return momentum


def calculate_ema_ratios(close: pd.Series, windows: list = [5, 10, 20, 30]) -> pd.DataFrame:
    """
    计算多周期EMA比率
    
    Args:
        close: 收盘价序列
        windows: EMA窗口列表
        
    Returns:
        包含各周期EMA比率的DataFrame
        
    标准化建议: np.log(ratio)
    """
    ratios = pd.DataFrame(index=close.index)
    for window in windows:
        ema = close.ewm(span=window).mean()
        ratios[f'ema_ratio_{window}'] = close / ema
    return ratios


def calculate_adx(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
    """
    计算平均趋向指数 (ADX)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        window: 计算窗口，默认14
        
    Returns:
        ADX值，范围 0-100
        
    标准化建议: adx / 100，将范围转换为 [0, 1]
    """
    # 计算真实波幅
    tr1 = high - low
    tr2 = abs(high - close.shift(1))
    tr3 = abs(low - close.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # 计算方向移动
    dm_plus = np.where((high - high.shift(1)) > (low.shift(1) - low), 
                       np.maximum(high - high.shift(1), 0), 0)
    dm_minus = np.where((low.shift(1) - low) > (high - high.shift(1)), 
                        np.maximum(low.shift(1) - low, 0), 0)
    
    dm_plus = pd.Series(dm_plus, index=high.index)
    dm_minus = pd.Series(dm_minus, index=high.index)
    
    # 计算平滑的DI
    tr_smooth = tr.rolling(window).mean()
    dm_plus_smooth = dm_plus.rolling(window).mean()
    dm_minus_smooth = dm_minus.rolling(window).mean()
    
    di_plus = 100 * dm_plus_smooth / tr_smooth
    di_minus = 100 * dm_minus_smooth / tr_smooth
    
    # 计算ADX
    dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus + 1e-8)
    adx = dx.rolling(window).mean()
    
    return adx
