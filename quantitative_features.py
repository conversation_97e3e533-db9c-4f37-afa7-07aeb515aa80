"""
量化特征工程模块
包含高级量化因子和市场结构特征
"""
import pandas as pd
import numpy as np
from typing import Union, Tuple, Optional, List


def calculate_price_rank(close: pd.Series, window: int = 252) -> pd.Series:
    """
    计算价格分位数排名
    
    Args:
        close: 收盘价序列
        window: 排名窗口，默认252日(一年)
        
    Returns:
        价格排名，范围 0-1，天然标准化
    """
    rank = close.rolling(window).rank(pct=True)
    return rank


def calculate_volume_rank(volume: pd.Series, window: int = 252) -> pd.Series:
    """
    计算成交量分位数排名
    
    Args:
        volume: 成交量序列
        window: 排名窗口，默认252日
        
    Returns:
        成交量排名，范围 0-1，天然标准化
    """
    rank = volume.rolling(window).rank(pct=True)
    return rank


def calculate_return_skewness(returns: pd.Series, window: int = 60) -> pd.Series:
    """
    计算收益率偏度
    
    Args:
        returns: 收益率序列
        window: 计算窗口，默认60日
        
    Returns:
        收益率偏度
        
    范围: 通常 -3 到 3
    标准化建议: np.tanh(skewness / 2)
    """
    skewness = returns.rolling(window).skew()
    return skewness


def calculate_return_kurtosis(returns: pd.Series, window: int = 60) -> pd.Series:
    """
    计算收益率峰度
    
    Args:
        returns: 收益率序列
        window: 计算窗口，默认60日
        
    Returns:
        收益率峰度
        
    范围: 通常 1 到 10
    标准化建议: np.tanh((kurtosis - 3) / 3)
    """
    kurtosis = returns.rolling(window).kurt()
    return kurtosis


def calculate_sharpe_ratio(returns: pd.Series, window: int = 60, risk_free_rate: float = 0.02) -> pd.Series:
    """
    计算滚动夏普比率
    
    Args:
        returns: 收益率序列
        window: 计算窗口，默认60日
        risk_free_rate: 无风险利率，默认2%年化
        
    Returns:
        夏普比率
        
    范围: 通常 -2 到 3
    标准化建议: np.tanh(sharpe / 2)
    """
    daily_rf = risk_free_rate / 252
    excess_returns = returns - daily_rf
    sharpe = excess_returns.rolling(window).mean() / (returns.rolling(window).std() + 1e-8)
    # 年化
    sharpe_annualized = sharpe * np.sqrt(252)
    return sharpe_annualized


def calculate_max_drawdown_rolling(returns: pd.Series, window: int = 60) -> pd.Series:
    """
    计算滚动最大回撤
    
    Args:
        returns: 收益率序列
        window: 计算窗口，默认60日
        
    Returns:
        滚动最大回撤
        
    范围: 通常 0 到 -0.5
    标准化建议: -drawdown (转为正值)
    """
    def rolling_max_drawdown(ret_series):
        cumulative = (1 + ret_series).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()
    
    rolling_dd = returns.rolling(window).apply(rolling_max_drawdown, raw=False)
    return rolling_dd


def calculate_beta(returns: pd.Series, market_returns: pd.Series, window: int = 60) -> pd.Series:
    """
    计算滚动Beta系数

    Args:
        returns: 个股收益率序列
        market_returns: 市场收益率序列
        window: 计算窗口，默认60日

    Returns:
        Beta系数

    范围: 通常 0 到 3
    标准化建议: np.tanh((beta - 1) / 1)
    """
    # 对齐数据
    aligned_data = pd.concat([returns, market_returns], axis=1).dropna()
    if len(aligned_data.columns) != 2:
        return pd.Series(np.nan, index=returns.index)

    aligned_data.columns = ['stock', 'market']

    # 计算滚动协方差和方差
    rolling_cov = aligned_data['stock'].rolling(window).cov(aligned_data['market'])
    rolling_var = aligned_data['market'].rolling(window).var()

    # 计算Beta
    beta = rolling_cov / (rolling_var + 1e-8)

    # 重新索引到原始索引
    result = pd.Series(np.nan, index=returns.index)
    result.loc[beta.index] = beta

    return result


def calculate_information_ratio(returns: pd.Series, benchmark_returns: pd.Series, window: int = 60) -> pd.Series:
    """
    计算信息比率
    
    Args:
        returns: 个股收益率序列
        benchmark_returns: 基准收益率序列
        window: 计算窗口，默认60日
        
    Returns:
        信息比率
        
    范围: 通常 -2 到 2
    标准化建议: np.tanh(ir / 1)
    """
    excess_returns = returns - benchmark_returns
    tracking_error = excess_returns.rolling(window).std()
    ir = excess_returns.rolling(window).mean() / (tracking_error + 1e-8)
    # 年化
    ir_annualized = ir * np.sqrt(252)
    return ir_annualized


def calculate_turnover_rate(volume: pd.Series, shares_outstanding: Union[float, pd.Series], window: int = 20) -> pd.Series:
    """
    计算换手率
    
    Args:
        volume: 成交量序列
        shares_outstanding: 流通股本
        window: 平滑窗口，默认20日
        
    Returns:
        换手率
        
    范围: 通常 0 到 1
    标准化建议: np.log(1 + turnover)
    """
    if isinstance(shares_outstanding, (int, float)):
        shares_outstanding = pd.Series(shares_outstanding, index=volume.index)
    
    daily_turnover = volume / shares_outstanding
    smoothed_turnover = daily_turnover.rolling(window).mean()
    return smoothed_turnover


def calculate_price_momentum_multiple_periods(close: pd.Series, periods: List[int] = [5, 10, 20, 60]) -> pd.DataFrame:
    """
    计算多周期价格动量
    
    Args:
        close: 收盘价序列
        periods: 动量计算周期列表
        
    Returns:
        包含各周期动量的DataFrame
        
    标准化建议: np.tanh(momentum / rolling_std(momentum, 252))
    """
    momentum_df = pd.DataFrame(index=close.index)
    for period in periods:
        momentum = close.pct_change(period)
        momentum_df[f'momentum_{period}d'] = momentum
    return momentum_df


def calculate_volatility_regime(returns: pd.Series, short_window: int = 20, long_window: int = 60) -> pd.Series:
    """
    计算波动率状态指标
    
    Args:
        returns: 收益率序列
        short_window: 短期波动率窗口
        long_window: 长期波动率窗口
        
    Returns:
        波动率状态比率
        
    范围: 通常 0.5 到 2.0
    标准化建议: np.log(ratio)
    """
    short_vol = returns.rolling(short_window).std()
    long_vol = returns.rolling(long_window).std()
    vol_regime = short_vol / (long_vol + 1e-8)
    return vol_regime


def calculate_mean_reversion_signal(close: pd.Series, window: int = 20, threshold: float = 2.0) -> pd.Series:
    """
    计算均值回归信号
    
    Args:
        close: 收盘价序列
        window: 移动平均窗口
        threshold: 标准差倍数阈值
        
    Returns:
        均值回归信号强度
        
    范围: -1 到 1，天然标准化
    """
    ma = close.rolling(window).mean()
    std = close.rolling(window).std()
    z_score = (close - ma) / (std + 1e-8)
    
    # 将z-score转换为信号强度
    signal = np.tanh(z_score / threshold)
    return signal


def calculate_trend_strength(close: pd.Series, window: int = 20) -> pd.Series:
    """
    计算趋势强度指标
    
    Args:
        close: 收盘价序列
        window: 计算窗口
        
    Returns:
        趋势强度
        
    范围: 0 到 1，天然标准化
    """
    # 使用线性回归斜率的R²作为趋势强度
    def trend_strength_calc(prices):
        if len(prices) < 3:
            return np.nan
        x = np.arange(len(prices))
        y = prices.values
        
        # 计算线性回归
        x_mean = x.mean()
        y_mean = y.mean()
        
        numerator = np.sum((x - x_mean) * (y - y_mean))
        denominator_x = np.sum((x - x_mean) ** 2)
        denominator_y = np.sum((y - y_mean) ** 2)
        
        if denominator_x == 0 or denominator_y == 0:
            return 0
        
        correlation = numerator / np.sqrt(denominator_x * denominator_y)
        r_squared = correlation ** 2
        
        return r_squared
    
    trend_strength = close.rolling(window).apply(trend_strength_calc, raw=False)
    return trend_strength


def calculate_support_resistance_levels(close: pd.Series, window: int = 60) -> Tuple[pd.Series, pd.Series]:
    """
    计算支撑阻力位
    
    Args:
        close: 收盘价序列
        window: 计算窗口
        
    Returns:
        (支撑位距离, 阻力位距离)，已标准化为相对距离
    """
    rolling_min = close.rolling(window).min()
    rolling_max = close.rolling(window).max()
    
    # 计算相对距离
    support_distance = (close - rolling_min) / close
    resistance_distance = (rolling_max - close) / close
    
    return support_distance, resistance_distance


def calculate_liquidity_score(volume: pd.Series, close: pd.Series, window: int = 20) -> pd.Series:
    """
    计算流动性评分
    
    Args:
        volume: 成交量序列
        close: 收盘价序列
        window: 计算窗口
        
    Returns:
        流动性评分
        
    范围: 0 到 1，天然标准化
    """
    # 使用成交额的变异系数的倒数作为流动性指标
    amount = volume * close
    amount_mean = amount.rolling(window).mean()
    amount_std = amount.rolling(window).std()
    
    cv = amount_std / (amount_mean + 1e-8)  # 变异系数
    liquidity_score = 1 / (1 + cv)  # 转换为0-1分数
    
    return liquidity_score


def calculate_market_cap_factor(close: pd.Series, shares_outstanding: Union[float, pd.Series]) -> pd.Series:
    """
    计算市值因子
    
    Args:
        close: 收盘价序列
        shares_outstanding: 流通股本
        
    Returns:
        对数市值
        
    标准化建议: 已使用对数变换
    """
    if isinstance(shares_outstanding, (int, float)):
        shares_outstanding = pd.Series(shares_outstanding, index=close.index)
    
    market_cap = close * shares_outstanding
    log_market_cap = np.log(market_cap + 1)
    
    return log_market_cap
